using Microsoft.Azure.Management.ContainerRegistry.Fluent.Models;
using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Application.Commands.ProdutoCommands.ProdutoCorTamanhoEstoqueCommands;
using Zendar.Business.AutoMappers.ProdutoMapper;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.PesquisaProdutoServices.Popular;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorImagemService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoServices.ProdutoCorTamanhoService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Mediator;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.DTO.Preco;

namespace Zendar.Business.Services.V2.ProdutoV2Services.ProdutoVariacaoFacade
{
    public class ProdutoVariacaoV2Facade : BaseService, IProdutoVariacaoV2Facade
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IMediatorHandler _mediator;
        private readonly IZendarTriggerService _zendarTriggerService;

        private readonly IProdutoCorV2Service _produtoCorV2Service;
        private readonly IProdutoCorImagemV2Service _produtoCorImagemV2Service;
        private readonly IProdutoCorTamanhoV2Service _produtoCorTamanhoV2Service;
        private readonly IPopularPesquisaProdutoService _popularPesquisaProdutoService;

        private readonly ICorRepository _corRepository;
        private readonly ITamanhoRepository _tamanhoRepository;
        private readonly IProdutoCorRepository _produtoCorRepository;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;

        public ProdutoVariacaoV2Facade(
            INotificador notificador,
            IAspNetUserInfo aspNetUserInfo,
            IMediatorHandler mediator,
            IZendarTriggerService zendarTriggerService,
            IProdutoCorV2Service produtoCorV2Service,
            IProdutoCorImagemV2Service produtoCorImagemV2Service,
            IProdutoCorTamanhoV2Service produtoCorTamanhoV2Service,
            IPopularPesquisaProdutoService popularPesquisaProdutoService,
            ICorRepository corRepository,
            ITamanhoRepository tamanhoRepository,
            IProdutoCorRepository produtoCorRepository,
            IProdutoCorTamanhoRepository produtoCorTamanhoRepository)
            : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _mediator = mediator;
            _zendarTriggerService = zendarTriggerService;
            _produtoCorV2Service = produtoCorV2Service;
            _produtoCorImagemV2Service = produtoCorImagemV2Service;
            _produtoCorTamanhoV2Service = produtoCorTamanhoV2Service;
            _popularPesquisaProdutoService = popularPesquisaProdutoService;
            _corRepository = corRepository;
            _tamanhoRepository = tamanhoRepository;
            _produtoCorRepository = produtoCorRepository;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
        }

        public void Dispose()
        {
            _produtoCorV2Service?.Dispose();
            _produtoCorImagemV2Service?.Dispose();
            _produtoCorTamanhoV2Service?.Dispose();
            _popularPesquisaProdutoService?.Dispose();

            _corRepository?.Dispose();
            _tamanhoRepository?.Dispose();
            _produtoCorRepository?.Dispose();
            _produtoCorTamanhoRepository?.Dispose();
        }

        public async Task<List<ProdutoCorTamanhoV2ViewModel>> ObterListaVariacao(
            TipoProduto tipoProduto,
            Guid produtoId)
        {
            List<ProdutoCorTamanho> listaProdutoCorTamanho = new();

            if (tipoProduto != TipoProduto.PRODUTO_VARIACAO)
            {
                listaProdutoCorTamanho =
                    await _produtoCorTamanhoRepository.Where(x => x.ProdutoCor.ProdutoId == produtoId)
                                                      .ToListAsync();
            }
            else if (tipoProduto == TipoProduto.PRODUTO_VARIACAO)
            {
                listaProdutoCorTamanho =
                    await _produtoCorTamanhoRepository.Where(produtoCorTamanho => produtoCorTamanho.ProdutoCor.ProdutoId == produtoId &&
                                                                                  (produtoCorTamanho.ProdutoCor.Produto.ProdutoCores.Count() == 1 || !produtoCorTamanho.ProdutoCor.Cor.PadraoSistema) &&
                                                                                  (produtoCorTamanho.ProdutoCor.ProdutoCorTamanhos.Count() == 1 || !produtoCorTamanho.Tamanho.PadraoSistema) &&
                                                                                  !(produtoCorTamanho.ProdutoCor.Cor.PadraoSistema && produtoCorTamanho.Tamanho.PadraoSistema))
                                                      .ToListAsync();
            }

            if (listaProdutoCorTamanho == null ||
                listaProdutoCorTamanho.Count == 0)
            {
                NotificarAvisoRegistroNaoEncontrada("variação padrão");

                return new();
            }

            return listaProdutoCorTamanho.Select(c => c.ToViewModel())
                                         .ToList();
        }

        #region [Variação Padrão]

        public async Task<ProdutoCorTamanhoV2ViewModel> ObterVariacaoPadrao(
            Guid produtoId)
        {
            var produtoCorTamanhoVariacaoPadrao =
                await _produtoCorTamanhoRepository.FirstOrDefaultAsNoTracking(x => x.ProdutoCor.ProdutoId == produtoId &&
                                                                                   x.ProdutoCor.Cor.PadraoSistema &&
                                                                                   x.Tamanho.PadraoSistema);

            if (produtoCorTamanhoVariacaoPadrao == null)
            {
                NotificarAvisoRegistroNaoEncontrada("variação padrão");

                return null;
            }

            return produtoCorTamanhoVariacaoPadrao.ToViewModel();
        }

        public async Task<bool> CadastrarVariacaoPadrao(
            ProdutoVariacaoPadraoV2ViewModel variacaoPadrao)
        {
            Cor corPadrao = null;

            Tamanho tamanhoPadrao = null;

            // obter cor padrao
            corPadrao =
                await _corRepository.FirstOrDefaultAsNoTracking(x => x.PadraoSistema,
                                                                     x => new Cor { Id = x.Id });
            // obter tamanho padrao
            tamanhoPadrao =
                await _tamanhoRepository.FirstOrDefaultAsNoTracking(x => x.PadraoSistema,
                                                                    x => new Tamanho { Id = x.Id });

            if (corPadrao == null || tamanhoPadrao == null)
            {
                NotificarAvisoRegistroNaoEncontrado("cor e/ou tamanho padrão");

                return false;
            }

            // inserir cor
            var produtoCorId =
                await CadastrarCor(variacaoPadrao.ProdutoId,
                                   corPadrao.Id);

            if (produtoCorId == default)
                return false;

            // inserir tamanho
            var produtoCorTamanhosId =
                await CadastrarTamanho(variacaoPadrao.ProdutoId,
                                       tamanhoPadrao.Id);

            if (produtoCorTamanhosId == null ||
                !produtoCorTamanhosId.Any() ||
                produtoCorTamanhosId.Any(x => x == default))
            {
                return false;
            }

            // recuperar variacao padrao
            ProdutoCorTamanhoV2ViewModel produtoCorTamanhoVariacaoPadrao =
                await ObterVariacaoPadrao(variacaoPadrao.ProdutoId);

            produtoCorTamanhoVariacaoPadrao.EstoqueMinimo = variacaoPadrao.EstoqueMinimo;
            produtoCorTamanhoVariacaoPadrao.EstoqueAtual = variacaoPadrao.EstoqueAtual;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.Altura = variacaoPadrao.OutrasInformacoesAltura;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.Largura = variacaoPadrao.OutrasInformacoesLargura;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.Profundidade = variacaoPadrao.OutrasInformacoesProfundidade;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.PesoLiquido = variacaoPadrao.OutrasInformacoesPesoLiquido;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.PesoBruto = variacaoPadrao.OutrasInformacoesPesoBruto;
            produtoCorTamanhoVariacaoPadrao.Caracteristicas.PesoEmbalagem = variacaoPadrao.OutrasInformacoesPesoEmbalagem;
            produtoCorTamanhoVariacaoPadrao.Identificadores.CodigoGTINEAN = variacaoPadrao.CodigoGTINEAN;
            produtoCorTamanhoVariacaoPadrao.Identificadores.CodigoExterno = variacaoPadrao.CodigoExterno;

            // alterar variacao padrao
            await _produtoCorTamanhoV2Service.Alterar(variacaoPadrao.ProdutoId, produtoCorTamanhoVariacaoPadrao);

            if (variacaoPadrao.ProdutoCorImagens != null &&
                variacaoPadrao.ProdutoCorImagens.Any())
            {
                foreach (var imagem in variacaoPadrao.ProdutoCorImagens)
                    imagem.ProdutoCorId = produtoCorId;

                await _produtoCorImagemV2Service.Cadastrar(variacaoPadrao.ProdutoCorImagens);
            }

            return true;
        }

        #endregion

        public async Task<List<ProdutoCorTamanhoEntradaXMLV2ViewModel>> ListarProdutoCoresTamanhosComGTINEAN(
            Guid produtoId,
            StatusConsulta status = StatusConsulta.Todos)
        {
            var query = _produtoCorRepository
                .Where(pc => pc.ProdutoId == produtoId);

            if (status == StatusConsulta.Ativos)
                query = query.Where(pc => pc.Ativo);
            else if (status == StatusConsulta.Inativos)
                query = query.Where(pc => !pc.Ativo);

            return await query
                .Select(pc => new ProdutoCorTamanhoEntradaXMLV2ViewModel
                {
                    Cor = new CorViewModel
                    {
                        Id = pc.Cor.Id,
                        Descricao = pc.Cor.Descricao,
                        DescricaoEcommerce = pc.Cor.DescricaoEcommerce,
                        PadraoSistema = pc.Cor.PadraoSistema,
                        Ativo = pc.Ativo,
                    },
                    Tamanhos = pc.ProdutoCorTamanhos
                        .Where(pct => status == StatusConsulta.Todos
                                   || (status == StatusConsulta.Ativos && pct.Ativo)
                                   || (status == StatusConsulta.Inativos && !pct.Ativo))
                        .Select(pct => new TamanhoComGTINEANViewModel
                        {
                            Id = pct.Tamanho.Id,
                            Descricao = pct.Tamanho.Descricao,
                            DescricaoEcommerce = pct.Tamanho.DescricaoEcommerce,
                            PadraoSistema = pct.Tamanho.PadraoSistema,
                            CodigoGTINEAN = pct.CodigoGTINEAN,
                            Ativo = pct.Ativo,
                            SequenciaOrdenacao = pct.Tamanho.SequenciaOrdenacao
                        })
                        .OrderBy(t => t.SequenciaOrdenacao)
                        .ToArray()
                })
                .OrderBy(pc => pc.Cor.Descricao)
                .ToListAsync();
        }

        #region [ProdutoCores]

        public async Task<List<ProdutoCorV2ViewModel>> ListarCoresProduto(
            Guid produtoId,
            StatusConsulta status = StatusConsulta.Todos)
        {
            var query =
                _produtoCorRepository.Where(x => x.ProdutoId == produtoId);

            if (status == StatusConsulta.Ativos)
            {
                query = query.Where(x => x.Ativo);
            }
            else if (status == StatusConsulta.Inativos)
            {
                query = query.Where(x => !x.Ativo);
            }

            var produtoCores = await query.Select(x => new ProdutoCor
            {
                Id = x.Id,
                Ativo = x.Ativo,
                Cor = new Cor
                {
                    Id = x.Cor.Id,
                    Descricao = x.Cor.Descricao,
                    DescricaoEcommerce = x.Cor.DescricaoEcommerce,
                    PadraoSistema = x.Cor.PadraoSistema
                }
            })
            .ToListAsync();

            if (produtoCores == null ||
                produtoCores.Count == 0)
            {
                return new();
            }

            return produtoCores.Where(pc => produtoCores.Count == 1 || !pc.Cor.PadraoSistema)
                               .Select(produtoCor => new ProdutoCorV2ViewModel
                               {
                                   Id = produtoCor.Id,
                                   CorId = produtoCor.Cor.Id,
                                   Cor = new CorViewModel
                                   {
                                       Id = produtoCor.Cor.Id,
                                       Descricao = produtoCor.Cor.Descricao,
                                       DescricaoEcommerce = produtoCor.Cor.DescricaoEcommerce,
                                       PadraoSistema = produtoCor.Cor.PadraoSistema,
                                       Ativo = produtoCor.Ativo,
                                   },
                                   Ativo = produtoCor.Ativo
                               })
                               .OrderBy(p => p.Cor.Descricao)
                               .ToList();
        }

        public async Task<Guid> CadastrarCor(
            Guid produtoId,
            Guid corId)
        {
            var produtoCorViewModel = new ProdutoCorV2ViewModel()
            {
                ProdutoId = produtoId,
                CorId = corId,
                Ativo = true
            };

            var produtoCorId =
                await _produtoCorV2Service.Cadastrar(produtoCorViewModel);

            if (produtoCorId == Guid.Empty)
                return default;

            var tamanhosProduto =
                await ListarTamanhosProduto(produtoId);

            if (!tamanhosProduto.Any())
                return produtoCorId;

            var produtoCorTamanhos =
                tamanhosProduto.Select(tamanho => new ProdutoCorTamanhoV2ViewModel(produtoCorId,
                                                                                   tamanho.Id,
                                                                                   tamanho.Ativo))
                               .ToArray();

            await _produtoCorTamanhoV2Service.Cadastrar(produtoCorTamanhos);

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);

            return produtoCorId;
        }

        public async Task AtivarInativarCor(
            Guid produtoId,
            Guid corId,
            bool ativar)
        {
            #region Cor

            var produtoCores =
                await _produtoCorRepository.Where(x => x.ProdutoId == produtoId &&
                                                       x.CorId == corId)
                                           .Select(x => x.Id)
                                           .ToArrayAsync();

            if (produtoCores == null ||
                !produtoCores.Any())
            {
                NotificarAvisoRegistroNaoEncontrada("variação de cor");

                return;
            }

            if (ativar)
                await _produtoCorV2Service.Ativar(produtoCores);
            else
                await _produtoCorV2Service.Inativar(produtoCores);

            #endregion

            #region Tamanho

            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(x => x.ProdutoCor.ProdutoId == produtoId &&
                                                              x.ProdutoCor.CorId == corId)
                                                  .Select(x => x.Id)
                                                  .ToArrayAsync();

            if (produtoCorTamanhos == null ||
                !produtoCorTamanhos.Any())
            {
                NotificarAvisoRegistroNaoEncontrada("variação de tamanho");

                return;
            }

            if (ativar)
                await _produtoCorTamanhoV2Service.Ativar(produtoCorTamanhos);
            else
                await _produtoCorTamanhoV2Service.Inativar(produtoCorTamanhos);

            #endregion
        }

        #endregion

        #region [ProdutoCorTamanhos]

        public async Task<List<TamanhoViewModel>> ListarTamanhosProduto(
            Guid produtoId,
            StatusConsulta status = StatusConsulta.Todos)
        {
            var query = _produtoCorTamanhoRepository.Where(x => x.ProdutoCor.ProdutoId == produtoId);

            if (status == StatusConsulta.Ativos)
            {
                query = query.Where(pct => pct.Ativo);
            }
            else if (status == StatusConsulta.Inativos)
            {
                query = query.Where(pct => !pct.Ativo && !pct.ProdutoCor.Cor.PadraoSistema && !pct.Tamanho.PadraoSistema);
            }

            var tamanhosProduto = await query.Select(x => new ProdutoCorTamanho
            {
                TamanhoId = x.TamanhoId,
                Tamanho = new Tamanho
                {
                    Id = x.Tamanho.Id,
                    Descricao = x.Tamanho.Descricao,
                    DescricaoEcommerce = x.Tamanho.DescricaoEcommerce,
                    PadraoSistema = x.Tamanho.PadraoSistema
                },
                Ativo = x.Ativo
            })
            .ToArrayAsync();

            if (tamanhosProduto == null ||
                !tamanhosProduto.Any())
                return new();

            var tamanhosIdDistintos =
                tamanhosProduto.Select(x => x.TamanhoId)
                               .Distinct();

            return tamanhosProduto.Where(pct => tamanhosIdDistintos.Count() == 1 || !pct.Tamanho.PadraoSistema)
                                  .GroupBy(x => x.Tamanho.Id)
                                  .Select(tamanho => new TamanhoViewModel()
                                  {
                                      Id = tamanho.Key,
                                      Descricao = tamanho.First().Tamanho.Descricao,
                                      DescricaoEcommerce = tamanho.First().Tamanho.DescricaoEcommerce,
                                      PadraoSistema = tamanho.First().Tamanho.PadraoSistema,
                                      Ativo = tamanho.Any(t => t.Ativo)
                                  })
                                  .OrderBy(p => p.SequenciaOrdenacao)
                                  .ToList();
        }

        public async Task<IEnumerable<Guid>> CadastrarTamanho(
            Guid produtoId,
            Guid tamanhoId)
        {
            var produtoCores =
                await _produtoCorRepository.Where(x => x.ProdutoId == produtoId)
                                           .Select(x => new ProdutoCor
                                           {
                                               Id = x.Id,
                                               ProdutoCorTamanhos = x.ProdutoCorTamanhos
                                               .Select(y => new ProdutoCorTamanho { Ativo = y.Ativo })
                                               .ToList()
                                           })
                                           .ToArrayAsync();

            if (produtoCores == null ||
                !produtoCores.Any())
            {
                NotificarAvisoRegistroNaoEncontrado("produto");

                return Enumerable.Empty<Guid>();
            }

            var produtoCorTamanhos =
                produtoCores.Select(produtoCor =>
                new ProdutoCorTamanhoV2ViewModel(produtoCor.Id,
                                                 tamanhoId,
                                                 !produtoCor.ProdutoCorTamanhos.Any() ||
                                                 !produtoCor.ProdutoCorTamanhos.All(x => !x.Ativo)))
                            .ToArray();

            await _produtoCorTamanhoV2Service.Cadastrar(produtoCorTamanhos);

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);

            return produtoCorTamanhos.Select(x => x.Id);
        }

        public async Task AtivarInativarTamanho(
            Guid produtoId,
            Guid tamanhoId,
            bool ativar)
        {
            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(x => x.ProdutoCor.ProdutoId == produtoId &&
                                                              x.TamanhoId == tamanhoId)
                                                  .Select(x => x.Id)
                                                  .ToArrayAsync();

            if (produtoCorTamanhos == null ||
                !produtoCorTamanhos.Any())
            {
                NotificarAvisoRegistroNaoEncontrada("variação de tamanho");

                return;
            }

            if (ativar)
                await _produtoCorTamanhoV2Service.Ativar(produtoCorTamanhos);
            else
                await _produtoCorTamanhoV2Service.Inativar(produtoCorTamanhos);
        }

        #endregion

        #region [Alteração em massa]

        public async Task AlterarCaracteristicasEmMassa(
            Guid produtoId,
            Guid[] produtoCorTamanhosId,
            ProdutoCorTamanhoCaracteristicasV2ViewModel caracteristicas)
        {
            if (produtoCorTamanhosId.Length == 0)
                return;

            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(pct => produtoCorTamanhosId.Contains(pct.Id))
                                                  .ToListAsync();

            if (produtoCorTamanhos.Count != produtoCorTamanhosId.Length)
            {
                NotificarAvisoRegistroNaoEncontrada("variação");

                return;
            }

            foreach (var produtoCorTamanho in produtoCorTamanhos)
            {
                produtoCorTamanho.Altura = caracteristicas.Altura;
                produtoCorTamanho.Largura = caracteristicas.Largura;
                produtoCorTamanho.Profundidade = caracteristicas.Profundidade;
                produtoCorTamanho.PesoLiquido = caracteristicas.PesoLiquido;
                produtoCorTamanho.PesoBruto = caracteristicas.PesoBruto;
                produtoCorTamanho.PesoEmbalagem = caracteristicas.PesoEmbalagem;
            }

            await _produtoCorTamanhoRepository.SaveChanges();

            await _zendarTriggerService.ExecuteGuid(produtoId,
                                                    TabelaTrigger.PRODUTO,
                                                    OperacaoTrigger.ALTERAR);
        }

        public async Task AlterarEstoqueEmMassa(
            Guid produtoId,
            Guid lojaId,
            Guid usuarioId,
            Guid[] produtoCorTamanhosId,
            decimal estoqueAtual,
            decimal estoqueMinimo)
        {
            if (produtoCorTamanhosId.Length == 0)
                return;

            var produtoCorTamanhos =
                await _produtoCorTamanhoRepository.Where(pct => produtoCorTamanhosId.Contains(pct.Id))
                                                  .ToListAsync();

            if (produtoCorTamanhos.Count != produtoCorTamanhosId.Length)
            {
                NotificarAvisoRegistroNaoEncontrada("variação");

                return;
            }

            foreach (var produtoCorTamanho in produtoCorTamanhos)
                produtoCorTamanho.EstoqueMinimo = estoqueMinimo;

            await _produtoCorTamanhoRepository.SaveChanges();

            await _zendarTriggerService.ExecuteGuid(produtoId,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.ALTERAR);

            // Só atualizar o estoque se o valor for maior que 0
            // Isso evita o aviso desnecessário quando apenas o estoque mínimo foi alterado
            if (estoqueAtual > 0)
            {
                var produtoCorTamanhoEstoques =
                    produtoCorTamanhosId.Select(pctId => (pctId, estoqueAtual, Guid.Empty))
                                        .ToArray();

                await _mediator.EnviarComando(
                    new AtualizarEstoqueVariacoesCommand(
                        produtoId,
                        _aspNetUserInfo.LojaId.Value,
                        Guid.Parse(_aspNetUserInfo.Id),
                        produtoCorTamanhoEstoques));
            }
        }

        #endregion

        public async Task<ProdutoVariacaoPrecoDTO> ObterComPreco(
            Guid variacaoId,
            Guid lojaId)
        {
            var precoVariacao =
                await _produtoCorTamanhoRepository.Where(x => x.Id == variacaoId)
                                                  .Select(ProdutoVariacaoPrecoDTO.FromProdutoCorTamanho(lojaId))
                                                  .FirstOrDefaultAsync();

            if (precoVariacao is null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");

                return null;
            }

            return precoVariacao;
        }
    }
}